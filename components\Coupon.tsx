import React from "react";
import { View, Text, TouchableOpacity, Animated, Image, StyleSheet, Dimensions } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useTheme } from "@/components/ThemeContext";

const { width } = Dimensions.get("window");
const defaultCouponWidth = width * 0.4;

// Define the interface for the coupon data
export interface CouponData {
  coupon_id: number;
  discount: string;
  short_description: string;
  long_description: string;
  image: string; // URL for the coupon image
  qr: string; // URL for the QR code
  special_offer: boolean;
  brand: {
    brand_id: number;
    displayed_name: string;
    logo: string; // URL for the brand logo
    link: string;
  };
}

// Define props interface for Coupon component
interface CouponProps {
  coupon: CouponData;
  userFavoriteBrandIds: number[];
  onToggleFavorite: (brandId: number) => Promise<boolean>;
  scaleAnim?: Animated.Value;
  onHeartPress?: (brandId: number) => void;
  width?: number;
  showHeart?: boolean;
  onPress?: () => void;
}

export default function Coupon({
  coupon,
  userFavoriteBrandIds,
  onToggleFavorite,
  scaleAnim,
  onHeartPress,
  width = defaultCouponWidth,
  showHeart = true,
  onPress
}: CouponProps) {
  const router = useRouter();
  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  const handleHeartPress = async () => {
    // Animate the heart icon if scaleAnim is provided
    if (scaleAnim) {
      Animated.sequence([
        Animated.timing(scaleAnim, { toValue: 1.3, duration: 120, useNativeDriver: true }),
        Animated.timing(scaleAnim, { toValue: 1, duration: 120, useNativeDriver: true }),
      ]).start();
    }

    // Call the onToggleFavorite callback
    await onToggleFavorite(coupon.brand.brand_id);

    // Call optional onHeartPress callback
    if (onHeartPress) {
      onHeartPress(coupon.brand.brand_id);
    }
  };

  const handleCouponPress = () => {
    if (onPress) {
      onPress();
    } else {
      // Default navigation to coupon detail page
      router.push({
        pathname: "/coupon",
        params: { couponId: coupon.coupon_id.toString() }
      });
    }
  };

  return (
    <TouchableOpacity
      style={[styles.couponCard, { width, borderColor: theme.primary }]}
      activeOpacity={0.92}
      onPress={handleCouponPress}
    >
      <View style={[styles.couponPercent, { backgroundColor: theme.primary }]}>
        <Text style={[styles.couponPercentText, { color: theme.text }]}>
          {coupon.discount}
        </Text>
      </View>

      {showHeart && (
        <TouchableOpacity
          style={styles.couponHeart}
          onPress={handleHeartPress}
          hitSlop={10}
        >
          {scaleAnim ? (
            <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
              <Ionicons
                name={userFavoriteBrandIds.includes(coupon.brand.brand_id) ? "heart" : "heart-outline"}
                size={32}
                color={theme.primary}
              />
            </Animated.View>
          ) : (
            <Ionicons
              name={userFavoriteBrandIds.includes(coupon.brand.brand_id) ? "heart" : "heart-outline"}
              size={32}
              color={theme.primary}
            />
          )}
        </TouchableOpacity>
      )}

      <Image source={{ uri: coupon.image }} style={styles.couponImage} />

      <View style={styles.couponBottom}>
        <Text style={[styles.couponBottomText, { color: theme.text }]}>
          {coupon.short_description}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  couponCard: {
    backgroundColor: "#000",
    borderRadius: 16,
    justifyContent: "flex-end",
    height: 180,
    position: "relative",
    borderWidth: 3,
    overflow: 'hidden',
  },
  couponPercent: {
    position: "absolute",
    top: 6,
    left: 6,
    borderRadius: 10,
    paddingVertical: 6,
    paddingHorizontal: 12,
    zIndex: 1,
  },
  couponPercentText: {
    fontSize: 14,
    fontWeight: "bold",
    zIndex: 1,
  },
  couponHeart: {
    position: "absolute",
    top: 4,
    right: 4,
    borderRadius: 10,
    zIndex: 1,
  },
  couponImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    borderRadius: 12,
  },
  couponBottom: {
    position: "absolute",
    alignItems: "center",
    paddingVertical: 4,
    paddingHorizontal: 4,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
  },
  couponBottomText: {
    fontSize: 16,
    textAlign: "center",
  },
});