import React, { createContext, useContext, useState, ReactNode } from 'react';
import { FontStyleName, fontStyles, getFontStyle } from '@/constants/Fonts';

interface FontContextType {
  getFontStyle: (styleName: FontStyleName) => typeof fontStyles[FontStyleName];
  createCustomStyle: (
    baseFontStyle: FontStyleName,
    overrides: Partial<typeof fontStyles[FontStyleName]>
  ) => typeof fontStyles[FontStyleName];
}

const FontContext = createContext<FontContextType | undefined>(undefined);

interface FontProviderProps {
  children: ReactNode;
}

export const FontProvider: React.FC<FontProviderProps> = ({ children }) => {
  const getFontStyleFromContext = (styleName: FontStyleName) => {
    return getFontStyle(styleName);
  };

  const createCustomStyle = (
    baseFontStyle: FontStyleName,
    overrides: Partial<typeof fontStyles[FontStyleName]>
  ) => {
    return {
      ...fontStyles[baseFontStyle],
      ...overrides,
    };
  };

  return (
    <FontContext.Provider value={{ 
      getFontStyle: getFontStyleFromContext, 
      createCustomStyle 
    }}>
      {children}
    </FontContext.Provider>
  );
};

export const useFont = () => {
  const context = useContext(FontContext);
  if (context === undefined) {
    throw new Error('useFont must be used within a FontProvider');
  }
  return context;
};
