import { Ionicons } from "@expo/vector-icons";
import { router, Tabs } from "expo-router";
import { Pressable, Text, StyleSheet, Platform, View, TextInput, TouchableOpacity, Image } from "react-native";
import  { useState } from "react";
import { useRouter } from "expo-router";
import { useTheme } from "@/components/ThemeContext"; // Import your useTheme hook

export default function TabLayout() {
  const searchIcon = Platform.OS === "ios" ? "search-outline" : "search";
  const homeIcon = Platform.OS === "ios" ? "home-outline" : "home";
  const pricetagIcon = Platform.OS === "ios" ? "pricetag-outline" : "pricetag";

  const { getCustomTheme } = useTheme(); // Access the custom theme hook
  const theme = getCustomTheme(); // Get the current theme's values

  // Keresés állapot a fejlécben
  const [search, setSearch] = useState("");
  const [searchVisible, setSearchVisible] = useState(false);
  const router = useRouter();
  return (
    <View
     style={{
      flexDirection: "row",
      justifyContent: "space-around",
      alignItems: "center",
      padding: 10,
      backgroundColor: theme.tertiary,
      }}
    >
      <Image
        source={require('../assets/images/EverDeal-Logo-White-110.png')}
        style={{ width: 192, height: 40 }}
        resizeMode="contain"
      />
      <Pressable
              onPress={() => {
                // TODO: Implement search functionality
                // console.log("Search pressed");
              }}
              style={({ pressed }) => ({
                opacity: pressed ? 0.5 : 1,
                marginRight: 10,
                marginLeft:"auto"
              })}
            >
              <Ionicons name={searchIcon} size={36} color={theme.primary} />
            </Pressable>
    </View>
  );
}
