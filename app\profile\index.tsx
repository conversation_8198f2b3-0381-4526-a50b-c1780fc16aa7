import React, { useState, useEffect, use<PERSON>allback } from "react";
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, ActivityIndicator } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import * as MailComposer from 'expo-mail-composer';
import { useTheme } from "@/components/ThemeContext";
import { useAuth } from "@/components/AuthContext";
import { getFavoriteBrandIds } from "@/components/FavsContext";
import { supabase } from "@/lib/supabase";

interface EmployeeProfile {
  name: string;
  email: string;
  gender: string | null;
  residence: string | null;
  occupation: string | null;
  saving_ft: number | null;
  interests: string[] | null;
}

const allCategories = [
  "Olvasás", "Elektronika", "Egészségügy", "<PERSON><PERSON>", "<PERSON>ul<PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ked<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Szórakozás",
];

export default function ProfilePage() {
  const router = useRouter();
  const { getCustomTheme, setTheme } = useTheme();
  const theme = getCustomTheme();
  const { employeeId, isLoadingAuth } = useAuth();

  const [editing, setEditing] = useState(false);
  const [profile, setProfile] = useState<EmployeeProfile | null>(null);
  const [loadingProfile, setLoadingProfile] = useState(true);
  
  // Note: 'age' and 'company' are not in the database schema, keeping hardcoded for now.
  const [age, setAge] = useState("33"); 
  const [company, setCompany] = useState("EverDeal");

  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [editInterests, setEditInterests] = useState(false);
  const [favoriteBrandsCount, setFavoriteBrandsCount] = useState<number>(0);
  
  // Placeholder for Activated Coupons (not in employee table schema)
  const activatedCouponsCount = 45; 

  // Function to fetch user profile data
  const fetchProfile = useCallback(async () => {
    if (!employeeId) {
      setLoadingProfile(false);
      return;
    }

    setLoadingProfile(true);
    const { data, error } = await supabase
      .from('employee')
      .select('name, email, gender, residence, occupation, saving_ft, interests')
      .eq('employee_id', employeeId) 
      .single();

    if (error || !data) {
      console.error("Error fetching profile:", error?.message);
      //Alert.alert("Hiba", "Nem sikerült betölteni a profil adatokat.");
      setProfile(null); // Ensure profile is null if fetching fails
    } else {
      setProfile(data as EmployeeProfile);
      // Initialize interests from database data, defaulting to empty array if null
      setSelectedCategories(data.interests || []);
    }
    setLoadingProfile(false);
  }, [employeeId]);

  useEffect(() => {
    if (!isLoadingAuth) {
      fetchProfile();
    }
  }, [isLoadingAuth, fetchProfile]);

  // Fetch user's favorite brands count
  useEffect(() => {
    async function fetchFavoriteBrandsCount() {
      if (employeeId && !isLoadingAuth) {
        try {
          const favoriteIds = await getFavoriteBrandIds(employeeId);
          setFavoriteBrandsCount(favoriteIds.length);
        } catch (error) {
          setFavoriteBrandsCount(0);
        }
      } else if (!employeeId && !isLoadingAuth) {
        setFavoriteBrandsCount(0);
      }
    }
    fetchFavoriteBrandsCount();
  }, [employeeId, isLoadingAuth]);

  // Function to handle opening in-app email composer
  const handleSendEmail = async () => {
    try {
      const isAvailable = await MailComposer.isAvailableAsync();

      if (isAvailable) {
        const result = await MailComposer.composeAsync({
          recipients: ['<EMAIL>'],
          subject: 'Kapcsolatfelvétel - EverDeal App',
          body: 'Kedves EverDeal Csapat,\n\n',
          isHtml: false,
        });

        if (result.status === MailComposer.MailComposerStatus.SENT) {
          Alert.alert(
            'Email elküldve',
            'Az email sikeresen elküldve!',
            [{ text: 'OK' }]
          );
        }
      } else {
        Alert.alert(
          'Email nem elérhető',
          'Az email funkció nem elérhető ezen az eszközön. Kérjük, írjon nekünk közvetlenül a következő címre: <EMAIL>',
          [
            { text: 'OK' }
          ]
        );
      }
    } catch (error) {
      Alert.alert(
        'Hiba',
        'Hiba történt az email megnyitása során. Kérjük, próbálja újra később.',
        [{ text: 'OK' }]
      );
    }
  };

  // Handle saving interests
  const handleSaveInterests = async () => {
    if (!employeeId) return;

    // Save selectedCategories to database
    const { data, error } = await supabase
      .from('employee')
      .update({ interests: selectedCategories })
      .eq('employee_id', employeeId);

    if (error) {
      Alert.alert("Hiba", "Nem sikerült menteni az érdeklődési köröket.");
    } else {
      setEditInterests(false);
    }
  };

  // Handle saving profile data (only for editable fields)
  const handleSaveProfile = async () => {
    if (!employeeId || !profile) return;

    // Update only fields that were edited. Note: 'age' and 'company' are not saved to DB as they are not in schema.
    const { error } = await supabase
      .from('employee')
      .update({
        email: profile.email,
        gender: profile.gender,
        residence: profile.residence,
        occupation: profile.occupation,
      })
      .eq('employee_id', employeeId);

    if (error) {
      Alert.alert("Hiba", "Nem sikerült menteni a profil adatokat.");
    } else {
      setEditing(false);
    }
  };

  if (loadingProfile || isLoadingAuth) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.bg }]}>
        <ActivityIndicator size="large" color={theme.primary} />
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.bg }]}>
        <Text style={{ color: theme.text, marginBottom: 20 }}>Profil adatok nem érhetők el.</Text>
        <TouchableOpacity 
          style={[styles.loginButton, { backgroundColor: theme.primary }]}
          onPress={() => router.replace('/')}
        >
          <Text style={[styles.loginButtonText, { color: theme.text }]}>Bejelentkezés</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.bg }]}>
      <View style={styles.header}>
        <Text style={styles.emoji}>🧑‍💻</Text>
        <Text style={[styles.name, { color: theme.text }]}>{profile.name}</Text>
      </View>

      <View style={[styles.savingsBox, { backgroundColor: theme.bg, borderColor: theme.primary }]}>
        <Text style={[styles.savingsAmount, { color: theme.text }]}>{profile.saving_ft} Ft</Text>
        <Text style={[styles.savingsLabel, { color: theme.primary }]}>Megtakarítás</Text>
      </View>

      <View style={styles.statsRow}>
        <View style={[styles.statBox, { backgroundColor: theme.bg, borderColor: theme.primary }]}>
          <Text style={[styles.statNumber, { color: theme.text }]}>{activatedCouponsCount}</Text>
          <Text style={[styles.statLabel, { color: theme.primary }]}>Aktivált Kupon</Text>
        </View>
        <TouchableOpacity
          style={[styles.statBox, { backgroundColor: theme.bg, borderColor: theme.primary }]}
          onPress={() => router.replace('/favs')}
          activeOpacity={0.7}
        >
          <Text style={[styles.statNumber, { color: theme.text }]}>{favoriteBrandsCount}</Text>
          <Text style={[styles.statLabel, { color: theme.primary }]}>Kedvenc Üzlet</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionRow}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>Érdeklődési körök</Text>
          <TouchableOpacity onPress={() => editInterests ? handleSaveInterests() : setEditInterests(true)}>
            <Ionicons name={editInterests ? "checkmark-outline" : "create-outline"} size={18} color={theme.primary} style={{ marginLeft: 8 }} />
          </TouchableOpacity>
        </View>
        {editInterests ? (
          <View style={styles.categoriesGrid}>
            {allCategories.map((cat) => {
              const selected = selectedCategories.includes(cat);
              return (
                <TouchableOpacity
                  key={cat}
                  style={[
                    styles.categoryChip,
                    selected
                      ? { backgroundColor: theme.primary }
                      : { backgroundColor: theme.text },
                  ]}
                  onPress={() => {
                    setSelectedCategories((prev) =>
                      selected ? prev.filter((c) => c !== cat) : [...prev, cat]
                    );
                  }}
                >
                  <Ionicons
                    name={selected ? "remove" : "add"}
                    size={16}
                    color={selected ? theme.text : theme.bg}
                    style={{ marginRight: 4 }}
                  />
                  <Text style={[styles.categoryText, { color: selected ? theme.text : theme.bg }]}>
                    {cat}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ) : (
          <View style={styles.interestsRow}>
            {selectedCategories.length > 0 ? (
              selectedCategories.map((cat) => (
                <View key={cat} style={[styles.interestChip, { backgroundColor: theme.primary }]}>
                  <Text style={[styles.interestText, { color: theme.text }]}>{cat}</Text>
                </View>
              ))
            ) : (
              <Text style={{ color: theme.text }}>Nincs érdeklődési kör kiválasztva.</Text>
            )}
          </View>
        )}
      </View>

      <View style={styles.sectionRow}>
        <Text style={[styles.sectionTitle, { color: theme.text }]}>Adatok</Text>
        <TouchableOpacity onPress={() => editing ? handleSaveProfile() : setEditing(true)}>
          <Ionicons name={editing ? "checkmark-outline" : "create-outline"} size={18} color={theme.primary} style={{ marginLeft: 8 }} />
        </TouchableOpacity>
      </View>
      <View style={[styles.infoBox, { backgroundColor: theme.bg, borderColor: theme.primary, borderWidth: 2 }]}>
        {editing ? (
          <>
            <TextInput
              style={[styles.input, { backgroundColor: theme.bg, borderColor: theme.secondary, borderWidth: 2, color: theme.text }]}
              value={profile.email}
              onChangeText={(t) => setProfile((p) => (p ? { ...p, email: t } : null))}
              placeholder="E-mail"
              placeholderTextColor={theme.text + '80'}
            />
            {/* Note: Age is not in DB schema, keeping local state */}
            <TextInput
              style={[styles.input, { backgroundColor: theme.bg, borderColor: theme.secondary, borderWidth: 2, color: theme.text }]}
              value={age}
              onChangeText={setAge}
              placeholder="Kor"
              placeholderTextColor={theme.text + '80'}
              keyboardType="numeric"
            />
            <TextInput
              style={[styles.input, { backgroundColor: theme.bg, borderColor: theme.secondary, borderWidth: 2, color: theme.text }]}
              value={profile.gender || ''}
              onChangeText={(t) => setProfile((p) => (p ? { ...p, gender: t } : null))}
              placeholder="Nem"
              placeholderTextColor={theme.text + '80'}
            />
            <TextInput
              style={[styles.input, { backgroundColor: theme.bg, borderColor: theme.secondary, borderWidth: 2, color: theme.text }]}
              value={profile.residence || ''}
              onChangeText={(t) => setProfile((p) => (p ? { ...p, residence: t } : null))}
              placeholder="Lakcím"
              placeholderTextColor={theme.text + '80'}
            />
            {/* Note: Company is not in DB schema, keeping local state */}
            <TextInput
              style={[styles.input, { backgroundColor: theme.bg, borderColor: theme.secondary, borderWidth: 2, color: theme.text }]}
              value={company}
              onChangeText={setCompany}
              placeholder="Cégem"
              placeholderTextColor={theme.text + '80'}
            />
            <TextInput
              style={[styles.input, { backgroundColor: theme.bg, borderColor: theme.secondary, borderWidth: 2, color: theme.text }]}
              value={profile.occupation || ''}
              onChangeText={(t) => setProfile((p) => (p ? { ...p, occupation: t } : null))}
              placeholder="Pozíció"
              placeholderTextColor={theme.text + '80'}
            />
          </>
        ) : (
          <Text style={[styles.infoText, { color: theme.text }]}>
            E-mail: {profile.email}{"\n"}
            Kor: {age}{"\n"}
            Nem: {profile.gender || 'N/A'}{"\n"}
            Lakcím: {profile.residence || 'N/A'}{"\n"}
            Cégem: {company}{"\n"}
            Pozíció: {profile.occupation || 'N/A'}
          </Text>
        )}
      </View>

      {/* Theme switching buttons */}
      <View style={styles.themeSwitchContainer}>
        <Text style={[styles.sectionTitle, { color: theme.text, marginBottom: 12 }]}>Téma választás</Text>
        <View style={styles.themeSwitchRow}>
          <TouchableOpacity
            style={[styles.themeSwitchButton, { backgroundColor: '#0E3154', borderColor: '#40E0D0' }]}
            onPress={() => setTheme('blue')}
          >
            <Text style={[styles.themeSwitchButtonText, { color: '#FFFFFF' }]}>Kék Téma</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.themeSwitchButton, { backgroundColor: '#40E0D0', borderColor: '#0E3154' }]}
            onPress={() => setTheme('cyan')}
          >
            <Text style={[styles.themeSwitchButtonText, { color: '#FFFFFF' }]}>Cián Téma</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.themeSwitchButton, { backgroundColor: '#FFFFFF', borderColor: '#40E0D0' }]}
            onPress={() => setTheme('white')}
          >
            <Text style={[styles.themeSwitchButtonText, { color: '#000000' }]}>Fehér Téma</Text>
          </TouchableOpacity>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.messageBox, { backgroundColor: theme.secondary }]}
        onPress={handleSendEmail}
        activeOpacity={0.7}
      >
        <Text style={[styles.messageText, { color: theme.primary }]}>Írj nekünk!</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.logoutButton, { backgroundColor: '#FF4444', borderColor: '#CC0000' }]}
        onPress={() => router.replace('/')}
        activeOpacity={0.7}
      >
        <Ionicons name="log-out-outline" size={20} color="#FFFFFF" style={{ marginRight: 8 }} />
        <Text style={[styles.logoutButtonText, { color: '#FFFFFF' }]}>Kijelentkezés</Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 18,
    marginTop: 8,
  },
  emoji: {
    fontSize: 32,
    marginRight: 10,
  },
  name: {
    fontSize: 22,
    fontWeight: "bold",
  },
  savingsBox: {
    borderRadius: 16,
    borderWidth: 2,
    alignItems: "center",
    paddingVertical: 18,
    marginBottom: 16,
  },
  savingsAmount: {
    fontSize: 28,
    fontWeight: "bold",
  },
  savingsLabel: {
    fontSize: 16,
    marginTop: 4,
  },
  statsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  statBox: {
    flex: 1,
    borderRadius: 16,
    borderWidth: 2,
    alignItems: "center",
    paddingVertical: 18,
    marginHorizontal: 4,
  },
  statNumber: {
    fontSize: 22,
    fontWeight: "bold",
  },
  statLabel: {
    fontSize: 14,
    marginTop: 4,
    textAlign: "center",
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontWeight: "bold",
    fontSize: 16,
    marginBottom: 8,
  },
  interestsRow: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
    gap: 8,
  },
  interestChip: {
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 6,
    marginRight: 10,
  },
  interestText: {
    fontWeight: "bold",
    fontSize: 14,
  },
  addChip: {
    borderRadius: 20,
    padding: 6,
    justifyContent: "center",
    alignItems: "center",
  },
  sectionRow: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
    marginBottom: 8,
  },
  infoBox: {
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 22,
  },
  messageBox: {
    borderRadius: 12,
    padding: 14,
    alignItems: "center",
    marginBottom: 24,
  },
  messageText: {
    fontWeight: "bold",
    fontSize: 16,
  },
  input: {
    borderRadius: 8,
    padding: 8,
    marginBottom: 8,
    fontSize: 14,
  },
  categoriesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    marginBottom: 8,
  },
  categoryChip: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 20,
    paddingHorizontal: 14,
    paddingVertical: 7,
    marginRight: 8,
    marginBottom: 8,
  },
  categoryText: {
    fontWeight: "bold",
    fontSize: 14,
  },
  themeSwitchContainer: {
    marginBottom: 16,
  },
  themeSwitchRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 8,
  },
  themeSwitchButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: "center",
  },
  themeSwitchButtonText: {
    fontWeight: "bold",
    fontSize: 14,
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 12,
    borderWidth: 2,
    padding: 14,
    marginBottom: 24,
    marginTop: 8,
  },
  logoutButtonText: {
    fontWeight: "bold",
    fontSize: 16,
  },
  loginButton: {
    padding: 12,
    borderRadius: 8,
    marginTop: 10,
    alignItems: 'center',
  },
  loginButtonText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
});