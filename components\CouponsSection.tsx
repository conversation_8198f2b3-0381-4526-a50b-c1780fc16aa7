import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, Animated, Dimensions, Image, StyleSheet, ScrollView, ActivityIndicator, Alert } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useTheme } from "@/components/ThemeContext";
import { supabase } from '@/lib/supabase.js'; // Adjust this path to your supabase client initialization

const { width } = Dimensions.get("window");
const couponWidth = width * 0.4;

// Define the interface for the coupon data fetched from Supabase
interface CouponData {
  coupon_id: number;
  discount: string;
  short_description: string;
  long_description: string;
  image: string; // URL for the coupon image
  qr: string; // URL for the QR code
  special_offer: boolean;
  brand: {
    brand_id: number; // This is the crucial ID for favorites
    displayed_name: string;
    logo: string; // URL for the brand logo
    link: string;
  };
}

// Define props interface for CouponsSection
interface CouponsSectionProps {
  userFavoriteBrandIds: number[]; // Array of brand IDs that are favorited
  onToggleFavorite: (brandId: number) => Promise<boolean>; // Callback to toggle favorite status
}

// Export as a default function component, accepting the defined props
export default function CouponsSection({ userFavoriteBrandIds, onToggleFavorite }: CouponsSectionProps) {
  const [coupons, setCoupons] = useState<CouponData[]>([]);
  const [topCouponsData, setTopCouponsData] = useState<CouponData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize scaleAnim with an empty array; it will be populated after data fetch
  const [scaleAnim, setScaleAnim] = useState<Animated.Value[]>([]);
  const [search, setSearch] = useState(""); // Assuming search functionality might be added later
  const router = useRouter();

  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  useEffect(() => {
    async function fetchCoupons() {
      setIsLoading(true);
      setError(null);
      try {
        const { data, error } = await supabase
          .from('coupon')
          .select(`
            coupon_id,
            discount,
            short_description,
            long_description,
            image,
            qr,
            special_offer,
            brand (
              brand_id,
              displayed_name,
              logo,
              link
            )
          `);

        if (error) {
          setError("Failed to load coupons. Please try again later.");
          return;
        }

        if (data) {
          const regularOffers: CouponData[] = [];
          const specialOffers: CouponData[] = [];

          data.forEach((coupon: any) => {
            // Transform the coupon to handle brand as array from Supabase
            const transformedCoupon = {
              ...coupon,
              brand: Array.isArray(coupon.brand) ? coupon.brand[0] : coupon.brand,
            };

            if (coupon.special_offer) {
              specialOffers.push(transformedCoupon as CouponData);
            } else {
              regularOffers.push(transformedCoupon as CouponData);
            }
          });

          setCoupons(regularOffers);
          setTopCouponsData(specialOffers);

          // Initialize Animated.Value for each fetched coupon
          const allCoupons = [...regularOffers, ...specialOffers];
          setScaleAnim(allCoupons.map(() => new Animated.Value(1)));
        }
      } catch (err) {
        setError("An unexpected error occurred.");
      } finally {
        setIsLoading(false);
      }
    }

    fetchCoupons();
  }, []); // Empty dependency array means this runs once on mount

  // Combine all coupons for the search filter and scaleAnim indexing
  const allDisplayCoupons = [...coupons, ...topCouponsData];

  const filteredCoupons = allDisplayCoupons.filter(
    (coupon) =>
      coupon.short_description.toLowerCase().includes(search.toLowerCase()) ||
      (coupon.brand && coupon.brand.displayed_name.toLowerCase().includes(search.toLowerCase()))
  );

  // New handleHeartPress to use the passed onToggleFavorite prop
  const handleHeartPress = async (brandId: number, idx: number) => {
    // Animate the heart icon
    if (scaleAnim[idx]) {
      Animated.sequence([
        Animated.timing(scaleAnim[idx], { toValue: 1.3, duration: 120, useNativeDriver: true }),
        Animated.timing(scaleAnim[idx], { toValue: 1, duration: 120, useNativeDriver: true }),
      ]).start();
    }

    // Call the onToggleFavorite callback passed from HomePage
    const success = await onToggleFavorite(brandId);
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.bg }]}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={[styles.loadingText, { color: theme.text }]}>Loading coupons...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.bg }]}>
        <Text style={[styles.errorText, { color: theme.text }]}>{error}</Text>
      </View>
    );
  }

  return (
    <>
      <View style={styles.couponsContainer}>
        <Text style={[styles.couponsTitle, { color: theme.text }]}>Ajánlatok számodra</Text>
        <View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ paddingRight: 8 }}>
            {coupons.map((coupon, idx) => (
              <TouchableOpacity
                key={coupon.coupon_id}
                style={[styles.couponCard, { width: couponWidth, marginRight: 12, borderColor: theme.primary }]}
                activeOpacity={0.92}
                onPress={() => router.push({pathname: "/coupon", params: { couponId: coupon.coupon_id.toString()}})}
              >
                <View style={[styles.couponPercent, { backgroundColor: theme.primary }]}>
                  <Text style={[styles.couponPercentText, { color: theme.text }]}>{coupon.discount}</Text>
                </View>
                <TouchableOpacity
                  style={styles.couponHeart}
                  // Pass the coupon's brand_id, not coupon_id
                  onPress={() => handleHeartPress(coupon.brand.brand_id, idx)}
                  hitSlop={10}
                >
                  {scaleAnim[idx] && (
                    <Animated.View style={{ transform: [{ scale: scaleAnim[idx] }] }}>
                      <Ionicons
                        // Use userFavoriteBrandIds from props to determine heart state
                        name={userFavoriteBrandIds.includes(coupon.brand.brand_id) ? "heart" : "heart-outline"}
                        size={32}
                        color={theme.primary}
                      />
                    </Animated.View>
                  )}
                </TouchableOpacity>
                <Image source={{ uri: coupon.image }} style={styles.couponImage} />
                <View style={styles.couponBottom}>
                  <Text style={[styles.couponBottomText, { color: theme.text }]}>{coupon.short_description}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
      <View style={styles.couponsContainer}>
        <Text style={[styles.couponsTitle, { color: theme.text }]}>Heti top kedvezmények</Text>
        <View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ paddingRight: 8 }}>
            {topCouponsData.map((coupon, idx) => (
              <TouchableOpacity
                key={coupon.coupon_id}
                style={[styles.couponCard, { width: couponWidth, marginRight: 12, borderColor: theme.primary }]}
                activeOpacity={0.92}
                onPress={() => router.push({ pathname: "/coupon", params: { couponId: coupon.coupon_id.toString()}})}
              >
                <View style={[styles.couponPercent, { backgroundColor: theme.primary }]}>
                  <Text style={[styles.couponPercentText, { color: theme.text }]}>{coupon.discount}</Text>
                </View>
                <TouchableOpacity
                  style={styles.couponHeart}
                  // Adjust index for topCouponsData to correctly map to scaleAnim
                  // Pass the coupon's brand_id, not coupon_id
                  onPress={() => handleHeartPress(coupon.brand.brand_id, coupons.length + idx)}
                  hitSlop={10}
                >
                  {scaleAnim[coupons.length + idx] && (
                    <Animated.View style={{ transform: [{ scale: scaleAnim[coupons.length + idx] }] }}>
                      <Ionicons
                        // Use userFavoriteBrandIds from props to determine heart state
                        name={userFavoriteBrandIds.includes(coupon.brand.brand_id) ? "heart" : "heart-outline"}
                        size={32}
                        color={theme.primary}
                      />
                    </Animated.View>
                  )}
                </TouchableOpacity>
                <Image source={{ uri: coupon.image }} style={styles.couponImage} />
                <View style={styles.couponBottom}>
                  <Text style={[styles.couponBottomText, { color: theme.text }]}>{coupon.short_description}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  couponsContainer: {
    paddingVertical: 12,
    marginLeft: 16,
    marginTop: -12,
  },
  couponsTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
  },
  couponCard: {
    backgroundColor: "#fff", // Keeping white for the card background, as it's a common design pattern for coupons
    borderRadius: 16,
    marginBottom: 16,
    alignItems: "center",
    justifyContent: "center",
    width: couponWidth,
    height: 180,
    position: "relative",
    borderWidth: 3,
  },
  couponPercent: {
    position: "absolute",
    top: 6,
    left: 6,
    borderRadius: 10,
    paddingVertical: 6,
    paddingHorizontal: 12,
    zIndex: 1,
  },
  couponPercentText: {
    fontSize: 16,
    fontWeight: "bold",
    zIndex: 1,
  },
  couponHeart: {
    position: "absolute",
    top: 4,
    right: 4,
    borderRadius: 10,
    zIndex: 1,
  },
  couponImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover', // or 'contain', 'stretch' depending on desired visual
    borderRadius: 12, // Optional: for rounded corners
  },
  couponBottom: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: "center",
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 8,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
  },
  couponBottomText: {
    fontSize: 16,
    textAlign: "center",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
});