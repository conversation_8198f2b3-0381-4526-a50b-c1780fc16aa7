/**
 * Font system for the EverDeal app.
 * Defines typography scales and font styles similar to the theme system.
 */

export type FontStyleName =
    'h1'
  | 'h2'
  | 'h3'
  //| 'h4'
  //| 'h5'
  //| 'h6'
  | 'body'
  //| 'bodySmall'
  //| 'caption'
  //| 'button'
  //| 'overline'
  ;

// Base font tokens
const fontTokens = {
  // Font families
  primary: 'System', // Default system font
  secondary: 'SpaceMono', // Already loaded in the app
  
  // Font weights
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semiBold: '600' as const,
  bold: '700' as const,
  
  // Line height multipliers
  tight: 1.1,
  normal: 1.4,
  relaxed: 1.6,
};

// Font style definitions
export const fontStyles: Record<FontStyleName, {
  fontSize: number;
  lineHeight: number;
  fontWeight: string;
  fontFamily: string;
  letterSpacing?: number;
}> = {
  h1: {
    fontSize: 24,
    lineHeight: 24 * fontTokens.tight,
    fontWeight: fontTokens.bold,
    fontFamily: fontTokens.primary,
    letterSpacing: -0.5,
  },
  h2: {
    fontSize: 18,
    lineHeight: 18 * fontTokens.tight,
    fontWeight: fontTokens.bold,
    fontFamily: fontTokens.primary,
    letterSpacing: -0.25,
  },
  h3: {
    fontSize: 16,
    lineHeight: 16 * fontTokens.normal,
    fontWeight: fontTokens.semiBold,
    fontFamily: fontTokens.primary,
  },
  /*
  h4: {
    fontSize: 20,
    lineHeight: 20 * fontTokens.normal,
    fontWeight: fontTokens.semiBold,
    fontFamily: fontTokens.primary,
  },
  h5: {
    fontSize: 18,
    lineHeight: 18 * fontTokens.normal,
    fontWeight: fontTokens.medium,
    fontFamily: fontTokens.primary,
  },
  h6: {
    fontSize: 16,
    lineHeight: 16 * fontTokens.normal,
    fontWeight: fontTokens.medium,
    fontFamily: fontTokens.primary,
  },
  */
  body: {
    fontSize: 14,
    lineHeight: 14 * fontTokens.normal,
    fontWeight: fontTokens.regular,
    fontFamily: fontTokens.primary,
  },
  /*
  caption: {
    fontSize: 12,
    lineHeight: 12 * fontTokens.normal,
    fontWeight: fontTokens.regular,
    fontFamily: fontTokens.primary,
  },
  button: {
    fontSize: 16,
    lineHeight: 16 * fontTokens.normal,
    fontWeight: fontTokens.semiBold,
    fontFamily: fontTokens.primary,
    letterSpacing: 0.5,
  },
  overline: {
    fontSize: 10,
    lineHeight: 10 * fontTokens.normal,
    fontWeight: fontTokens.medium,
    fontFamily: fontTokens.primary,
    letterSpacing: 1.5,
  },
  */
};

// Helper function to get font style
export const getFontStyle = (styleName: FontStyleName) => {
  return fontStyles[styleName];
};

// Helper function to create custom font style
export const createFontStyle = (
  baseFontStyle: FontStyleName,
  overrides: Partial<typeof fontStyles[FontStyleName]>
) => {
  return {
    ...fontStyles[baseFontStyle],
    ...overrides,
  };
};
