/**
 * Hook for using typography styles in components.
 * Provides easy access to font styles and utilities.
 */

import { useFont } from '@/components/FontContext';
import { useTheme } from '@/components/ThemeContext';
import { FontStyleName } from '@/constants/Fonts';

export function useTypography() {
  const { getFontStyle, createCustomStyle } = useFont();
  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  // Helper function to get a complete text style with theme color
  const getTextStyle = (
    variant: FontStyleName,
    color: 'primary' | 'secondary' | 'tertiary' | 'text' | 'bg' | string = 'text'
  ) => {
    const fontStyle = getFontStyle(variant);
    
    // Determine text color
    let textColor: string;
    if (color in theme) {
      textColor = theme[color as keyof typeof theme];
    } else {
      textColor = color; // Assume it's a direct color value
    }

    return {
      ...fontStyle,
      color: textColor,
    };
  };

  // Helper function to create themed text style with custom overrides
  const createThemedTextStyle = (
    variant: FontStyleName,
    color: 'primary' | 'secondary' | 'tertiary' | 'text' | 'bg' | string = 'text',
    overrides?: Partial<ReturnType<typeof getFontStyle>>
  ) => {
    const baseStyle = getTextStyle(variant, color);
    return {
      ...baseStyle,
      ...overrides,
    };
  };

  return {
    getFontStyle,
    createCustomStyle,
    getTextStyle,
    createThemedTextStyle,
    theme,
  };
}
