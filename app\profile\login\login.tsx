import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Image,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter, useLocalSearchParams } from "expo-router";
import { useTheme } from "@/components/ThemeContext"; // Import your useTheme hook
import { H1, H2, Body } from "@/components/ThemedTypography";
import { useTypography } from "@/hooks/useTypography";
import { supabase } from "@/lib/supabase";
import { Alert } from "react-native";
import { useEffect } from "react";

export default function LoginPage() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { getCustomTheme } = useTheme(); // Access the custom theme hook
  const theme = getCustomTheme(); // Get the current theme's values

  // Initialize state from route parameters if available (when returning from verification)
  const [email, setEmail] = useState((params.savedEmail as string) || "");
  const [aszfChecked, setAszfChecked] = useState(params.savedAszf === "true");
  const [adatChecked, setAdatChecked] = useState(params.savedAdat === "true");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const canContinue = email.length > 0 && aszfChecked && adatChecked;

  // Generate a 6-digit verification code
  const generateVerificationCode = (): string => {
    return Math.floor(100000 + Math.random() * 900000).toString();
  };

  // Send verification email automatically
  const sendVerificationEmail = async (email: string, code: string): Promise<boolean> => {
    try {
      // In a real production app, you would call your backend API here
      // Example: await fetch('/api/send-verification-email', { method: 'POST', body: JSON.stringify({ email, code }) })

      // For development/demo purposes, we'll simulate automatic sending
      const now = new Date();
      const timestamp = `${String(now.getFullYear()).slice(-2)}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}. ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
      console.log(`[${timestamp}] Verification email sent to ${email} with code: ${code}`);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In production, this would return the actual API response
      return true; // Simulate successful sending

    } catch (error) {
      console.error('Error sending verification email:', error);
      Alert.alert(
        "Email küldési hiba",
        "Hiba történt az email küldése során. Kérjük, próbálja újra.",
        [{ text: "OK" }]
      );
      return false;
    }
  };

  const handleLogin = async () => {
    if (!email || !aszfChecked || !adatChecked) {
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Check if email exists in the employee table
      const { data, error: dbError } = await supabase
        .from('employee')
        .select('email')
        .eq('email', email.trim().toLowerCase())
        .single();

      if (dbError || !data) {
        // Email not found in database
        let errorMessage = "Ez az e-mail cím nem található a rendszerben. Kérjük, ellenőrizze az e-mail címet vagy vegye fel a kapcsolatot a rendszergazdával.";

        // In development, show all available emails
        if (__DEV__) {
          try {
            const { data: allEmails, error: fetchError } = await supabase
              .from('employee')
              .select('email')
              .order('email');

            if (!fetchError && allEmails && allEmails.length > 0) {
              const emailList = allEmails.map(emp => emp.email).join('\n• ');
              errorMessage += `\n\nElérhető e-mail címek:\n• ${emailList}`;
            }
          } catch (devError) {
            // If fetching all emails fails, just show the original error
            console.log('Failed to fetch emails for dev mode:', devError);
          }
        }

        setError(errorMessage);
        /*
        *
        Alert.alert(
          "Érvénytelen e-mail",
          errorMessage,
          [{ text: "OK" }]
        );
        */
        setLoading(false);
        return;
      }

      // Email exists, generate and send verification code
      const verificationCode = generateVerificationCode();

      // Send verification email
      const emailSent = await sendVerificationEmail(email, verificationCode);

      if (emailSent) {
        // Store the verification code temporarily (in a real app, store this securely on backend)
        // For now, we'll pass it as a parameter (not secure for production)
        router.push({
          pathname: "/profile/login/verification",
          params: {
            email,
            verificationCode, // In production, don't pass this in URL
            savedEmail: email,
            savedAszf: aszfChecked.toString(),
            savedAdat: adatChecked.toString()
          },
        });
      } else {
        setError("Nem sikerült elküldeni az ellenőrző kódot. Kérjük, próbálja újra.");
      }
    } catch (err) {
      setError("Hiba történt a bejelentkezés során. Kérjük, próbálja újra.");
      Alert.alert(
        "Hiba",
        "Hiba történt a bejelentkezés során. Kérjük, próbálja újra.",
        [{ text: "OK" }]
      );
    } finally {
      setLoading(false);
    }
  };

  // Custom checkbox component
  function CustomCheckbox({
    value,
    onValueChange,
  }: Readonly<{
    value: boolean;
    onValueChange: (newValue: boolean) => void;
  }>) {
    return (
      <TouchableOpacity
        onPress={() => onValueChange(!value)}
        style={{
          width: 24,
          height: 24,
          borderWidth: 2,
          borderColor: value ? theme.primary : theme.text,
          backgroundColor: value ? theme.primary : "transparent",
          borderRadius: 6,
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {value && (
          <Text style={{ color: theme.text, fontSize: 18, fontWeight: "bold", lineHeight: 20 }}>✓</Text>
        )}
      </TouchableOpacity>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.bg }]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoid}
      >
        <View style={styles.innerContainer}>
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              marginBottom: 8,
            }}
          >
            <Image
              source={require('../../../assets/images/EverDeal-Logo-White-110.png')}
              style={{ width: 240, height: 50 }}
              resizeMode="contain"
            />
          </View>

          <View style={styles.formContainer}>
            <H1 color="text" style={{ textAlign: "center", marginBottom: 24 }}>Bejelentkezés</H1>

            <View style={styles.inputContainer}>
              <H2 color="text">E-mail cím</H2>
              <TextInput
                style={[styles.input, { color: "#000" }]}
                placeholder="E-mail cím megadása..."
                placeholderTextColor="#aaa"
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
                keyboardType="email-address"
                editable={!loading}
              />
            </View>

            {/* Error message */}
            {error ? (
              <View style={styles.errorContainer}>
                <Body color="#ff0000" style={{ textAlign: "center" }}>
                  {error}
                </Body>
              </View>
            ) : null}

            {/* Checkboxes */}
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginBottom: 8,
              }}
            >
              <CustomCheckbox value={aszfChecked} onValueChange={setAszfChecked} />
              <Body color="text" style={{ marginLeft: 8 }}>Elfogadom az ÁSZF-et</Body>
            </View>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginBottom: 16,
              }}
            >
              <CustomCheckbox value={adatChecked} onValueChange={setAdatChecked} />
              <Body color="text" style={{ marginLeft: 8 }}>
                Hozzájárulok adataim statisztikai célokra történő felhasználásához
              </Body>
            </View>

            <TouchableOpacity
              style={[
                styles.button,
                canContinue && !loading
                  ? { backgroundColor: theme.primary }
                  : { backgroundColor: theme.secondary },
              ]}
              onPress={handleLogin}
              disabled={!canContinue || loading}
            >
              {loading ? (
                <Body color="text">Ellenőrzés...</Body>
              ) : (
                <H1 color="text">Tovább</H1>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
      {/* Back Button at the bottom */}
      <View style={styles.backButtonContainer}>
        <TouchableOpacity
          style={styles.backButton}
          activeOpacity={0.85}
          onPress={() => router.replace('/')}
        >
          <H1 color="text">Vissza</H1>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    //backgroundColor: "#0A233C",
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  keyboardAvoid: {
    flex: 1,
  },
  innerContainer: {
    flex: 1,
    padding: 24,
    justifyContent: "center",
  },
  formContainer: {
    marginTop: 40, // Reduced margin to move content higher
  },
  errorContainer: {
    backgroundColor: "rgba(255, 68, 68, 0.1)",
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(255, 68, 68, 0.3)",
  },

  inputContainer: {
    marginBottom: 20,
  },

  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: "#f9f9f9",
  },
  button: {
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: "center",
    marginTop: 16,
  },

  // --- Back Button Styles ---
  backButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    alignItems: 'center',
    paddingBottom: 18,
    zIndex: 10,
  },
  backButton: {
    backgroundColor: '#21756C',
    borderRadius: 18,
    paddingVertical: 12,
    paddingHorizontal: 32,
    marginBottom: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },

});
