import { DefaultTheme, ThemeProvider } from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { Ionicons } from "@expo/vector-icons";
import { useEffect } from "react";
import MainTabs from "@/components/MainTabs";
import TopBar from "@/components/topbar";
import { FavouritesProvider } from "@/components/FavouritesContext";
import { SafeAreaView } from "react-native-safe-area-context";
import { ThemeContextProvider, useTheme } from "@/components/ThemeContext";
import { FontProvider } from "@/components/FontContext";
import { AuthProvider } from '@/components/AuthContext';
import { usePathname } from "expo-router";

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });
  
  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <ThemeContextProvider>
      <FontProvider>
        <InnerLayout />
      </FontProvider>
    </ThemeContextProvider>
  );
}

// Separate inner layout so we can use the context hook inside the provider
function InnerLayout() {
  const { getNavigationTheme } = useTheme();
  const { getCustomTheme } = useTheme(); // Access the custom theme hook
  const theme = getCustomTheme(); // Get the current theme's values
  const pathname = usePathname();

  // Define which pages should show the MainTabs
  // You can easily modify this array to control which pages have tabs
  const pagesWithTabs = [
    '/home',
    '/favs',
    '/map',
    '/profile'
  ];

  // Define which pages should show the TopBar
  // You can easily modify this array to control which pages have the topbar
  const pagesWithTopBar = [
    '/home',
    '/favs',
    '/map',
    '/profile'
  ];

  // Check if current page should show tabs and topbar
  const shouldShowTabs = pagesWithTabs.includes(pathname);
  const shouldShowTopBar = pagesWithTopBar.includes(pathname);

  return (
    <AuthProvider>
      <FavouritesProvider>
        <SafeAreaView style={{ flex: 1, backgroundColor: theme.tertiary }}>
          <ThemeProvider value={getNavigationTheme()}>
            {shouldShowTopBar && <TopBar />}
            <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen name="index" />
              <Stack.Screen name="home/index" options={{ headerShown: false }} />
              {/* Other screens can go here */}
            </Stack>
            {shouldShowTabs && <MainTabs />}
          </ThemeProvider>
        </SafeAreaView>
      </FavouritesProvider>
    </AuthProvider>
  );
}